import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'services/database_service.dart';
import 'services/camera_service.dart';
import 'services/face_detection_service.dart';
import 'utils/constants.dart';
import 'utils/permissions.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize services
  await _initializeServices();

  runApp(const FaceRecognitionApp());
}

Future<void> _initializeServices() async {
  try {
    // Initialize database
    final dbService = DatabaseService();
    await dbService.database; // This will create the database if it doesn't exist

    // Initialize face detection service
    final faceDetectionService = FaceDetectionService();
    await faceDetectionService.initialize();

    debugPrint('All services initialized successfully');
  } catch (e) {
    debugPrint('Error initializing services: $e');
  }
}

class FaceRecognitionApp extends StatelessWidget {
  const FaceRecognitionApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppConstants.appName,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: AppConstants.primaryColor,
          brightness: Brightness.light,
        ),
        useMaterial3: true,
        appBarTheme: const AppBarTheme(
          centerTitle: true,
          elevation: 0,
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            minimumSize: const Size(double.infinity, AppConstants.buttonHeight),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            ),
          ),
        ),
        cardTheme: CardThemeData(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          ),
        ),
      ),
      home: const SplashScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      // Check and request permissions
      final permissionsGranted = await PermissionHelper.handleAppStartupPermissions(context);

      if (!permissionsGranted) {
        _showPermissionError();
        return;
      }

      // Initialize camera service
      final cameraService = CameraService();
      final cameraInitialized = await cameraService.initialize();

      if (!cameraInitialized) {
        _showCameraError();
        return;
      }

      // Navigate to camera screen after delay
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const CameraScreen()),
        );
      }
    } catch (e) {
      debugPrint('Error initializing app: $e');
      _showInitializationError();
    }
  }

  void _showPermissionError() {
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: const Text('Permissions Required'),
          content: const Text('This app requires camera and storage permissions to function properly.'),
          actions: [
            TextButton(
              onPressed: () => _initializeApp(),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }
  }

  void _showCameraError() {
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: const Text('Camera Error'),
          content: const Text('Failed to initialize camera. Please check if camera is available and try again.'),
          actions: [
            TextButton(
              onPressed: () => _initializeApp(),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }
  }

  void _showInitializationError() {
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: const Text('Initialization Error'),
          content: const Text('Failed to initialize the app. Please restart the application.'),
          actions: [
            TextButton(
              onPressed: () => _initializeApp(),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.primaryColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App Icon/Logo
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(60),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: const Icon(
                Icons.face,
                size: 60,
                color: AppConstants.primaryColor,
              ),
            ),
            const SizedBox(height: 32),

            // App Name
            const Text(
              AppConstants.appName,
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),

            // App Subtitle
            const Text(
              'Smart Attendance System',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white70,
              ),
            ),
            const SizedBox(height: 48),

            // Loading Indicator
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
            const SizedBox(height: 16),

            const Text(
              'Initializing...',
              style: TextStyle(
                fontSize: 14,
                color: Colors.white70,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Placeholder HomeScreen - we'll implement this properly later
class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Face Recognition Attendance'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.face,
              size: 100,
              color: AppConstants.primaryColor,
            ),
            SizedBox(height: 24),
            Text(
              'Welcome to Face Recognition Attendance',
              style: AppConstants.headingStyle,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),
            Text(
              'App is ready! Camera and face detection services are initialized.',
              style: AppConstants.bodyStyle,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

// Camera Screen for Face Detection
class CameraScreen extends StatefulWidget {
  const CameraScreen({super.key});

  @override
  State<CameraScreen> createState() => _CameraScreenState();
}

class _CameraScreenState extends State<CameraScreen> {
  final CameraService _cameraService = CameraService();
  final FaceDetectionService _faceDetectionService = FaceDetectionService();
  bool _isDetecting = false;
  String _detectionStatus = 'Looking for faces...';
  final List<String> _detectedStudents = [];

  @override
  void initState() {
    super.initState();
    _startFaceDetection();
  }

  Future<void> _startFaceDetection() async {
    try {
      if (_cameraService.isInitialized) {
        await _cameraService.startImageStream(_onImageReceived);
        setState(() {
          _detectionStatus = 'Camera ready - Looking for students...';
        });
      }
    } catch (e) {
      setState(() {
        _detectionStatus = 'Error starting camera: $e';
      });
    }
  }

  void _onImageReceived(CameraImage image) async {
    if (_isDetecting) return;

    setState(() {
      _isDetecting = true;
    });

    try {
      // Process the image for face detection
      final result = await _faceDetectionService.detectFacesInCameraImage(image);

      if (result.faces.isNotEmpty) {
        setState(() {
          _detectionStatus = 'Found ${result.faces.length} face(s) - Processing...';
        });

        // Here you would add face recognition logic
        // For now, we'll just show that faces were detected
        await Future.delayed(const Duration(milliseconds: 500));

        setState(() {
          _detectionStatus = 'Faces detected! Add recognition logic here.';
        });
      } else {
        setState(() {
          _detectionStatus = 'Looking for faces...';
        });
      }
    } catch (e) {
      setState(() {
        _detectionStatus = 'Detection error: $e';
      });
    } finally {
      setState(() {
        _isDetecting = false;
      });
    }
  }

  @override
  void dispose() {
    _cameraService.stopImageStream();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Student Detection'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.flip_camera_ios),
            onPressed: () async {
              await _cameraService.switchCamera();
              setState(() {});
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Camera Preview
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              child: _cameraService.controller != null &&
                     _cameraService.controller!.value.isInitialized
                  ? CameraPreview(_cameraService.controller!)
                  : const Center(
                      child: CircularProgressIndicator(),
                    ),
            ),
          ),

          // Detection Status
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: AppConstants.primaryColor.withValues(alpha: 0.1),
            child: Column(
              children: [
                Text(
                  _detectionStatus,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
                if (_isDetecting)
                  const Padding(
                    padding: EdgeInsets.only(top: 8),
                    child: LinearProgressIndicator(),
                  ),
              ],
            ),
          ),

          // Detected Students List
          if (_detectedStudents.isNotEmpty)
            Expanded(
              flex: 1,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Detected Students:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Expanded(
                      child: ListView.builder(
                        itemCount: _detectedStudents.length,
                        itemBuilder: (context, index) {
                          return Card(
                            child: ListTile(
                              leading: const Icon(Icons.person),
                              title: Text(_detectedStudents[index]),
                              trailing: const Icon(
                                Icons.check_circle,
                                color: Colors.green,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  // This widget is the home page of your application. It is stateful, meaning
  // that it has a State object (defined below) that contains fields that affect
  // how it looks.

  // This class is the configuration for the state. It holds the values (in this
  // case the title) provided by the parent (in this case the App widget) and
  // used by the build method of the State. Fields in a Widget subclass are
  // always marked "final".

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  int _counter = 0;

  void _incrementCounter() {
    setState(() {
      // This call to setState tells the Flutter framework that something has
      // changed in this State, which causes it to rerun the build method below
      // so that the display can reflect the updated values. If we changed
      // _counter without calling setState(), then the build method would not be
      // called again, and so nothing would appear to happen.
      _counter++;
    });
  }

  @override
  Widget build(BuildContext context) {
    // This method is rerun every time setState is called, for instance as done
    // by the _incrementCounter method above.
    //
    // The Flutter framework has been optimized to make rerunning build methods
    // fast, so that you can just rebuild anything that needs updating rather
    // than having to individually change instances of widgets.
    return Scaffold(
      appBar: AppBar(
        // TRY THIS: Try changing the color here to a specific color (to
        // Colors.amber, perhaps?) and trigger a hot reload to see the AppBar
        // change color while the other colors stay the same.
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        // Here we take the value from the MyHomePage object that was created by
        // the App.build method, and use it to set our appbar title.
        title: Text(widget.title),
      ),
      body: Center(
        // Center is a layout widget. It takes a single child and positions it
        // in the middle of the parent.
        child: Column(
          // Column is also a layout widget. It takes a list of children and
          // arranges them vertically. By default, it sizes itself to fit its
          // children horizontally, and tries to be as tall as its parent.
          //
          // Column has various properties to control how it sizes itself and
          // how it positions its children. Here we use mainAxisAlignment to
          // center the children vertically; the main axis here is the vertical
          // axis because Columns are vertical (the cross axis would be
          // horizontal).
          //
          // TRY THIS: Invoke "debug painting" (choose the "Toggle Debug Paint"
          // action in the IDE, or press "p" in the console), to see the
          // wireframe for each widget.
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Text('You have pushed the button this many times:'),
            Text(
              '$_counter',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _incrementCounter,
        tooltip: 'Increment',
        child: const Icon(Icons.add),
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }
}
